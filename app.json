{"expo": {"name": "chefpal", "slug": "chefpal", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"googleServicesFile": "./GoogleService-Info.plist", "supportsTablet": true, "bundleIdentifier": "ai.yourchefpal.chefpal", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "NSCameraUsageDescription": "This app uses the camera to take pictures of ingredients for recipe suggestions."}}, "android": {"googleServicesFile": "./google-services.json", "package": "ai.yourchefpal.chefpal", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "@react-native-firebase/app", "@react-native-firebase/analytics", ["expo-build-properties", {"ios": {"useFrameworks": "static"}}], ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["react-native-share", {"ios": ["fb", "instagram", "twitter", "tiktoksharesdk"], "android": ["com.facebook.katana", "com.instagram.android", "com.twitter.android", "com.zhiliaoapp.musically"], "enableBase64ShareAndroid": true}]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "fedf55c4-e165-40a9-b787-402a4260657f"}}}}